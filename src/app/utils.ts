import { BindNamesAndValuesForPropertEntity } from './common/constants';
import { DatePipe } from '@angular/common';
import { AbstractControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { EnumCondoTypeName, EnumCondoTypeNameFromTiles } from './enumerations/condoType';
import { OfficeControls } from './enumerations/officeControlKeys';
import { UseTypes } from './enumerations/useTypes';
import { FormType } from './enumerations/formType';
import { CommonStrings, MeasurementTypes } from './constants';
import { RetailControls } from './enumerations/RetailControlKeys';
import { environment } from '../environments/environment';
import { ResearchType } from './enumerations/researchType';
import { UnitConversionEnum } from './enumerations/unitConversion';

export function getInt(value) {
    if (value) {
        const match = value.toString().match(/\d+/);
        if (match && match.length > 0 && match[0]) {
            return TryParseInt(match[0], match[0]);
        }
    }
    if (value === 0) { return 0; } else { return -1; }
}

export function TryParseInt(str: any, defaultValue: any): any {
    let retValue = defaultValue;
    if (str !== null) {
        if (str.length > 0) {
            if (!isNaN(str)) {
                retValue = parseInt(str, 10);
            } else {
                retValue = str.toLowerCase();
            }
        }
    } else {
        retValue = '';
    }
    return retValue;
}

export function clearSessionStorage(keys) {
    keys.forEach(key => {
      sessionStorage.removeItem(key);
    });
  }

export function areElementsMissing(array) {
for (let i = 0; i < array.length - 1; i++) {
    if (array[i + 1] - array[i] > 1) {
    return true; // Elements are missing
    }
}
return false; // No missing elements
}

export function numericValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    const value = control.value;
    if (value === null || value === '' || value === undefined) {
      return null; // Allow empty values
    }
    // Regular expression to match integers or decimals
    const valid = /^[0-9]+(\.[0-9]+)?$/.test(value.toString().trim());
    return valid ? null : { 'numeric': { value: control.value } };
  };
}

export function updateValidation(value: number, controlName: string, propertyKey: string, controlsMap, propertyDetails, form) {
  const control = form?.get(controlsMap[controlName]);
  if (!control) {
    return;
  }
  if (value > 0) {
    control?.enable();
    control?.setValidators([Validators.required]);
  } else {
    control?.disable();
    control?.clearValidators();
    if (control?.value) {
      control.setValue(null);
      control?.markAsDirty();
    }
    if (propertyDetails[propertyKey]) {
      propertyDetails[propertyKey] = null;
      control?.markAsDirty();
    }
  }
  control?.updateValueAndValidity();
}

export const yearBuiltValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const yearBuilt = control?.value;
  const yearRenovated = control?.parent?.get(OfficeControls?.YearRenovated)?.value;
  const currentYear = new Date().getFullYear();

  if (!yearBuilt) {
    return null;
  }

  if (yearBuilt > currentYear || (yearRenovated && yearBuilt > yearRenovated)) {
    return { errMsg: CommonStrings?.ErrorMessages?.YearBuildError };
  }

  return null;
};

export const yearRenovatedValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const yearRenovated = control?.value;
  const yearBuilt = control?.parent?.get(OfficeControls?.YearBuilt)?.value;
  const currentYear = new Date().getFullYear();

  if (!yearRenovated) {
    return null;
  }

  if (yearRenovated > currentYear || (yearBuilt && yearRenovated < yearBuilt)) {
    return { errMsg: CommonStrings?.ErrorMessages?.YearRenovatedError };
  }

  return null;
};

export const GRESBScoreMinValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const gresbScoreMin = control?.value;
  const gresbScoreMax = control?.parent?.get(OfficeControls?.GRESBScoreMax)?.value;

  if (!gresbScoreMin) {
    return null;
  }

  if ((gresbScoreMin && gresbScoreMax) && (Number(gresbScoreMin) > Number(gresbScoreMax))) {
    return { errMsg: CommonStrings?.ErrorMessages?.GresbScoreMinErrorMessage };
  }

  return null;
};


export const GRESBScoreMaxValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const gresbScoreMax = control?.value;
  const gresbScoreMin = control?.parent?.get(OfficeControls?.GRESBScoreMin)?.value;

  if (!gresbScoreMax) {
    return null;
  }

  if ((gresbScoreMin && gresbScoreMax) && (Math.trunc(gresbScoreMin) > Math.trunc(gresbScoreMax))) {
    return { errMsg: CommonStrings?.ErrorMessages?.GresbScoreMinErrorMessage };
  }

  return null;
};

export const clearHeightMinValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const clearHeightMin = Math.trunc(control?.value);
  const clearHeightMax = Math.trunc(control?.parent?.get(RetailControls?.ClearHeightMax)?.value);

  if (!clearHeightMin) {
    return null;
  }

  if (clearHeightMin && (clearHeightMin < 3 || clearHeightMin > 999)) {
    return { errMsg: CommonStrings?.ErrorMessages?.ClearHeightValueError };
  } else if ((clearHeightMin && clearHeightMax) && (Math.trunc(clearHeightMin) > Math.trunc(clearHeightMax))) {
    return { errMsg: CommonStrings?.ErrorMessages?.ClearHeightMinErrorMessage };
  }

  return null;
};


export const clearHeightMaxValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const clearHeightMax = Math.trunc(control?.value);
  const clearHeightMin = Math.trunc(control?.parent?.get(RetailControls?.ClearHeightMin)?.value);

  if (!clearHeightMax) {
    return null;
  }

  if (clearHeightMax && (clearHeightMax < 3 || clearHeightMax > 999)) {
    return { errMsg: CommonStrings?.ErrorMessages?.ClearHeightValueError };
  } else if ((clearHeightMin && clearHeightMax) && (clearHeightMin > clearHeightMax)) {
    return { errMsg: CommonStrings?.ErrorMessages?.ClearHeightMinErrorMessage };
  }

  return null;
};

export const minFloorValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const minFloorSize = control?.value;
  const maxFloorSize = control?.parent?.get(OfficeControls?.LargestFloor)?.value;
  const buildingSize = control?.parent?.parent?.get('MeasuredBuildingSizeSF')?.value;

  if (!minFloorSize) {
    return null;
  }

  if ((buildingSize && (parseFloat(minFloorSize) > parseFloat(buildingSize))) || (maxFloorSize && parseFloat(maxFloorSize) < parseFloat(minFloorSize))) {
    return { errMsg: CommonStrings?.ErrorMessages?.MinFloorErrorValidationMessage };
  }

  return null;
};


export const maxFloorValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const maxFloorSize = control?.value;
  const minFloorSize = control?.parent?.get(OfficeControls?.SmallestFloor)?.value;
  const buildingSize = control?.parent?.parent?.get('MeasuredBuildingSizeSF')?.value;

  if (!maxFloorSize) {
    return null;
  }

  if ((buildingSize && (parseFloat(maxFloorSize) > parseFloat(buildingSize))) || (minFloorSize && parseFloat(maxFloorSize) < parseFloat(minFloorSize))) {
    return { errMsg: CommonStrings?.ErrorMessages?.MaxFloorErrorValidationMessage };
  }

  return null;
};


const minMaxFloorCheck = (property, controlName) => {
  const buildingSize = parseFloat(property?.MeasuredBuildingSizeSF);
  const minFloorSize = parseFloat(property?.SmallestFloor);
  const maxFloorSize = parseFloat(property?.LargestFloor);
  let hasSizeError = false;
  if (controlName === OfficeControls?.SmallestFloor && ((buildingSize && (minFloorSize > buildingSize)) || (maxFloorSize && (maxFloorSize < minFloorSize)))) {
    hasSizeError = true;
  } else if (controlName === OfficeControls?.LargestFloor && ((buildingSize && (maxFloorSize > buildingSize)) || (minFloorSize && (minFloorSize > maxFloorSize)))) {
    hasSizeError = true;
  }
  return hasSizeError;
}

export const validateBuildingSize = (buildingSize, property, useTypeForm) => {

  // conditions to validate
  const conditions = [
    { controlName: OfficeControls?.OfficeSF, value: parseFloat(property?.OfficeSF) },
    { controlName: OfficeControls?.LargestFloor, value: parseFloat(property?.LargestFloor) },
    { controlName: OfficeControls?.SmallestFloor, value: parseFloat(property?.SmallestFloor) },
  ];

  // Check each condition
  conditions?.forEach(({ controlName, value }) => {
    const control = useTypeForm?.controls[controlName];

    if (controlName === OfficeControls?.LargestFloor && minMaxFloorCheck(property, controlName)) {
      control?.setErrors({ errMsg: CommonStrings?.ErrorMessages?.MinFloorErrorValidationMessage });
    } else if (controlName === OfficeControls?.SmallestFloor && minMaxFloorCheck(property, controlName)) {
      control?.setErrors({ errMsg: CommonStrings?.ErrorMessages?.MaxFloorErrorValidationMessage });
    } else {
      control?.setErrors(null);
    }
  });
}

export function integerGreaterThanZeroValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (value === null || value === '' || value == undefined) {
      return null; // Allow empty values
    }
    // Check if the value is an integer and greater than 0
    const isValid = value ? Number.isInteger(value) && value > 0 : true;

    // Return an error object if invalid
    return isValid ? null : { notIntegerOrNonPositive: true };
  };
}

export const websitePattern = '^((https?|ftp)://)?([A-Za-z]+\\.)?[A-Za-z0-9-]+(\\.[a-zA-Z]{1,4}){1,2}/?([a-zA-Z0-9\-\.\?\,\'\/\\\+&%\$#_]*)?$';

export function validateIntegerInput(event: KeyboardEvent, allowDecimal: boolean = false): void {
  const key = event.key;
  const inputElement = event.target as HTMLInputElement;
  const currentValue = inputElement.value;

  // Allow digits, navigation keys, and backspace/delete
  if (/^\d$/.test(key) || key === 'Backspace' || key === 'ArrowLeft' || key === 'ArrowRight' || key === 'Delete') {
    return;
  }

  // Allow decimal point only if allowDecimal is true and there is no existing decimal point
  if (allowDecimal && key === '.' && !/^\d*\.\d*$/.test(currentValue)) {
    return;
  }

  // Prevent input if it's not allowed
  event.preventDefault();
}

export function validatePasteInput(event: ClipboardEvent, allowDecimal: boolean = false): void {
  const pastedText = event.clipboardData?.getData('text') || '';

  // Define regex pattern based on allowDecimal flag
  const pattern = allowDecimal ? /^\d*(\.\d*)?$/ : /^\d+$/;

  // Check if the pasted text matches the allowed pattern
  if (!pattern.test(pastedText)) {
    event.preventDefault(); // Prevent pasting invalid content
  }
}
export function getRange(min, max) {
    if (min > max) {
      return []; // Return an empty array if min is greater than max
    }
  
    return Array.from({ length: max - min + 1 }, (_, i) => min + i);
  }

export function arrayToString(List: any) {
  let result = '';
  if (List) {
    if (List.length > 0) {
      for (const item of List) {
        result = result + item + ',';
      }
      return result.slice(0, -1);
    }
  } else {
    return null;
  }
}

export function hasCommonValue(list1: string, list2: string): boolean {
  if (!list1 || !list2) {
    return false;
  }

  const values1 = list1.split(',').map(value => value.trim());
  const values2 = list2.split(',').map(v => v.trim());

  return values1.some(value => values2.includes(value));
}

export function updateParcelInfo(existingParcelInfo, parcelFromTiles) {
  // Check if masterParcelFromTiles is not null or undefined
  if (parcelFromTiles) {
      // If existingParcelInfo exists, concatenate the new data
      if (existingParcelInfo) {
          return existingParcelInfo + ',' + parcelFromTiles;
      } else {
          // If existingParcelInfo does not exist, return parcelFromTiles
          return parcelFromTiles;
      }
  }
  // Return existingParcelInfo if parcelFromTiles is null or undefined
  return existingParcelInfo;
}

export function removeElementFromList(array: any[], value: any): void {
  if (array && array.length > 0) {
    const index = array.indexOf(value);
    if (index > -1) {
      array.splice(index, 1);
    }
  }
}

export function setPropertyName(locationDetails, addressTypeValues, streetPrefixes, streetSufixes, quadrants) {
  if (!locationDetails.PropertyName) {
    locationDetails.PropertyName = '';
    if (locationDetails.AddressType == addressTypeValues.Address) {
      if (locationDetails.StreetNumberMin)
        locationDetails.PropertyName = locationDetails.StreetNumberMin.toString();
      if (locationDetails.StreetNumberMax) {
        if (locationDetails.StreetNumberMin == locationDetails.StreetNumberMax) {
          locationDetails.PropertyName += ' ';
        } else {
          locationDetails.PropertyName += locationDetails.StreetNumberMin ? ' -' : '';
          locationDetails.PropertyName += ` ${locationDetails.StreetNumberMax.toString()} `;
        }
      } else {
        locationDetails.PropertyName += ' ';
      }

      if (locationDetails.StreetPrefix1) {
        locationDetails.PropertyName = locationDetails.PropertyName + streetPrefixes.find(x => x.PrefixID == locationDetails.StreetPrefix1).Prefix + ' ';
      }
      if (locationDetails.AddressStreetName) {
        locationDetails.PropertyName = locationDetails.PropertyName + locationDetails.AddressStreetName + ' ';
      }
      if (locationDetails.StreetSuffix1) {
        locationDetails.PropertyName = locationDetails.PropertyName + streetSufixes.find(x => x.SuffixId == locationDetails.StreetSuffix1).Suffix + ' ';
      }
      if (locationDetails.StreetSuffix2) {
        locationDetails.PropertyName = locationDetails.PropertyName + streetSufixes.find(x => x.SuffixId == locationDetails.StreetSuffix2).Suffix + ' ';
      }
    }
    else {
      if (locationDetails.Quadrant) {
        locationDetails.PropertyName = locationDetails.PropertyName + quadrants.find(x => x.QuadrantID == locationDetails.Quadrant).QuadrantName + ' ';
      }
      if (locationDetails.EastWestStreet) {
        locationDetails.PropertyName = locationDetails.PropertyName + locationDetails.EastWestStreet + ' ';
      }
      if (locationDetails.NorthSouthStreet) {
        locationDetails.PropertyName = locationDetails.PropertyName + locationDetails.NorthSouthStreet + ' ';
      }
    }
  }
}

export function formatDateForDatePicker(dateToBeFormatted) {
  if (dateToBeFormatted) {
    const parsedDate = parseBackendDate(dateToBeFormatted);
    const resultDate = {
      isRange: false,
      singleDate: {
        date: parsedDate,
        jsDate: new Date(dateToBeFormatted),
        epoc: Math.floor(new Date(dateToBeFormatted).getTime() / 1000),
      },
    };
    return resultDate;
  }
}

export function parseBackendDate(isoDate: string): { year: number; month: number; day: number } {
  const date = new Date(isoDate);
  return {
    year: date.getUTCFullYear(),
    month: date.getUTCMonth() + 1,
    day: date.getUTCDate(),
  };
}

export function getCondoTypeForUnitLabel(CondoTypeID){
  switch (CondoTypeID) {
    case EnumCondoTypeName.Strata:
      return EnumCondoTypeNameFromTiles.Strata;
    case EnumCondoTypeName.Child_Freehold:
      return EnumCondoTypeNameFromTiles.Freehold;
  }
}

export function stringToArray(strValue) {
  const returnArray = [];
  if (strValue) {
    const array = strValue.split(',');
    array.forEach(value => {
      returnArray.push(parseInt(value, 0));
    });
    return returnArray;
  } else {
    return null;
  }
}
function getFieldType (type) {
  return type === OfficeControls.BuildingClass ? 'ClassTypeID' : type
}

export function buildChangeLog(type, dataArray, Value, previousData, EntityID) {
  const date = new Date().toISOString();
  type = getFieldType(type);
  const i = dataArray.findIndex(x => x.Field === type);
  if (i !== -1) {
    dataArray.splice(i, 1);
  }
  dataArray.push({
    'Field': getFieldType(type),
    'CurrentValue': Value,
    'PreviousValue': previousData,
    'LoginEntityID': EntityID,
    'DateTime': date
  });
  return dataArray;
}

export function getPreviousData(Type, id, dropdownData) {
  let previousData
  const fieldId = BindNamesAndValuesForPropertEntity[Type]?.id;
  const fieldName = BindNamesAndValuesForPropertEntity[Type]?.name;
  if (dropdownData && fieldId && fieldName) {
    dropdownData.forEach(element => {
      if (element[fieldId] === id) {
        previousData = element[fieldName];
      }
    });
  }
  return previousData;
}

export function getFormattedDate(dateToFormat: string, dateFormat: string, timezone: string = '+0000'): string | null {
  return new DatePipe('en-US').transform(dateToFormat, dateFormat, timezone);
}

export function getFormNameByUseType(useTypeId: number): string | null {
  switch (useTypeId) {
    case UseTypes.Office:
    case UseTypes.Apartments:
    case UseTypes.SpecialUse:
      return FormType?.OfficeForm;
    case UseTypes.Industrial:
      return FormType?.IndustrialForm;
    case UseTypes.Retail:
      return FormType?.RetailForm;
    case UseTypes.Land:
      return FormType?.LandForm;
    default:
      return null;
  }
}

export function isValidWebsite(value: string | null): boolean {
  const regex = new RegExp(websitePattern);
  return value ? regex.test(value) : true; // Treat empty value as valid
}

export function setResearchStatusPin(emp: any): void {
  const iconMap = {
    [ResearchType.NeedsResearch]: environment.MapIconNeedsResearch,
    [ResearchType.BaseComplete]: environment.MapIconBaseResearchComplete,
    [ResearchType.FieldResearchComplete]: environment.MapIconFieldResearchComplete,
    [ResearchType.Hidden]: environment.MapIconHidden,
    [ResearchType.ExpressComplete]: environment.MapIconExpressComplete,
    [ResearchType.ExpressIncomplete]: environment.MapIconExpressIncomplete,
    [ResearchType.NotStarted]: environment.MapIconNotStarted,
  };

  emp.ResearchStatusPin = iconMap[emp.PropertyResearchTypeID] ?? environment.MapIconNotStarted;
}

export function getSizeOfNewStrataUnit(linkedProperties: any = [], unitId : number = 1, noOfUnits: number = 1) {
  const masterStrataProperty = linkedProperties?.find(property => property?.IsMaster);
  const masterStrataSize = unitId === UnitConversionEnum.Metric ? masterStrataProperty?.MeasuredBuildingSizeSM : masterStrataProperty?.MeasuredBuildingSizeSF;
  const strataProperties = linkedProperties?.filter(property => !property.IsMaster);
  // Sum ContributedGBA of footprint available units
  const totalCGBAFootprintAvailable = strataProperties?.filter(property => property.HasNoBuildingFootprints).reduce((sum, property) => {
      const valueToAdd = unitId === UnitConversionEnum.Metric ? (property.ContributedGBASM || 0) : (property.ContributedGBASF || 0);
      return sum + valueToAdd;
    }, 0);
  // Remaining size of master strata unit
  const remainingSize = parseFloat(masterStrataSize) - totalCGBAFootprintAvailable;
  // Calculate size per new unit
  // Count of PIDs to distribute master size includes the footprint available units and the new units
  // This is to ensure that the master strata size is distributed evenly among all units
  const strataPropertiesWithFootprint = strataProperties?.filter(property => !property.HasNoBuildingFootprints);
  const countOfPidsToDistributeMasterSize = strataPropertiesWithFootprint.length + noOfUnits;
  const sizePerNewUnit = remainingSize / countOfPidsToDistributeMasterSize;
  return sizePerNewUnit > 0 ? sizePerNewUnit : 0;
}

export function getSizeValue(allProperties, rowData, unitId) {
  if (rowData.IsMaster) {
    const childProperties = allProperties.filter(property => !property?.IsMaster);
    if (childProperties.length > 0 && childProperties.every((strata) => strata?.HasNoBuildingFootprints)) {
      return unitId === UnitConversionEnum.Metric ? rowData.ContributedGBASM : rowData.ContributedGBASF;
    } else {
      return unitId === UnitConversionEnum.Metric ? rowData.MeasuredBuildingSizeSM : rowData.MeasuredBuildingSizeSF;
    }
  } else {
    if (unitId === UnitConversionEnum.Metric) {
      return rowData.HasNoBuildingFootprints ? rowData.ContributedGBASM : rowData.MeasuredBuildingSizeSM;
    } else {
      return rowData.HasNoBuildingFootprints ? rowData.ContributedGBASF : rowData.MeasuredBuildingSizeSF;
    }
  }
}

function getMeasurementTypeKey(allProperties, rowData): keyof typeof MeasurementTypes {
  if (rowData.IsMaster) {
    const childProperties = allProperties.filter(property => !property?.IsMaster);

    if (childProperties.length > 0 && childProperties.every(property => property?.HasNoBuildingFootprints)) {
      return 'CONTRIBUTED';
    }
    if (childProperties.length > 0 && childProperties.some(property => property?.HasCalculatedValue)) {
      return 'ESTIMATED';
    }
    return rowData.IsAverageEstimationEnabled ? 'AVERAGEESTIMATION' : 'AERIAL';
  }
  if (rowData.HasNoBuildingFootprints) {
    return 'CONTRIBUTED';
  }
  if (rowData.HasCalculatedValue) {
    return 'ESTIMATED';
  }
  const master = allProperties.find(p => p?.IsMaster);
  return master?.IsAverageEstimationEnabled ? 'AVERAGEESTIMATION' : 'AERIAL';
}

export function getMeasurementTypeTitle(allProperties, rowData) {
  const key = getMeasurementTypeKey(allProperties, rowData);
  return MeasurementTypes[key].title;
}

export function getMeasurementTypeClassname(allProperties, rowData) {
  const key = getMeasurementTypeKey(allProperties, rowData);
  return MeasurementTypes[key].class;
}

export function getMeasurementTypeLabel(allProperties, rowData) {
  const key = getMeasurementTypeKey(allProperties, rowData);
  return MeasurementTypes[key].label;
}
