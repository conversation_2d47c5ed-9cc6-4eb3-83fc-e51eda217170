import { Component, OnInit, Input, NgZone, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { Subscription } from 'rxjs';
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { CommunicationService, CommunicationModel } from '../../services/communication.service';
import { PropertyService } from '../../services/api-property.service';
import { LoginService } from '../../services/login.service';
import { UnitConversionEnum } from '../../enumerations/unitConversion';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { IMetaData, MetaDataIndexedDBService } from '../../services/indexed-db-service.service';
import { MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { Property } from '../../models/Property';
import { PropertyTypes } from '../../enumerations/enums';
import { EnumCondoTypeName } from '../../enumerations/condoType';
import { getMeasurementTypeClassname, getMeasurementTypeLabel, getMeasurementTypeTitle, getSizeValue } from '../../../app/utils';

@Component({
    selector: 'app-strata',
    templateUrl: './strata.component.html',
    styleUrls: ['./strata.component.scss']
})
export class StrataComponent implements OnInit {
    @Input() initialDetails: mapEditPropertyDTO;
    @Output() showPropertyInfo = new EventEmitter();
    @Input() property: Property;
    @Input() selectedUseTypeID: any;
    @Output() addNewStrataUnit: EventEmitter<any> = new EventEmitter<any>();
    updateFormSubscription: Subscription;
    propertyStrataList: any[];
    propertiesLoading: boolean = false;
    strataTableHeader: any;
    BuildingSizeHeader = '';
    BuildingSizeValue: any = '';
    LotSizeValue: any = '';
    UnitId: number;
    UnitDisplayTextSize: any;
    propertyId: any;
    visitedStrataIds: any[];
    visitedPropertyIds: any = [];
    editedPropertyIds: any = [];
    editedStrataIds: any = [];
    isNavigationFromSearch: any;
    metaDataIndexedDBService: MetaDataIndexedDBService;
    showAddStrataBtn = false;
    useTypesToEnableAddStrataBtns = [PropertyTypes.Apartments, PropertyTypes.Office, PropertyTypes.Retail, PropertyTypes.Industrial];
    constructor(private communicationService: CommunicationService,
        private zone: NgZone,
        private _propertyService: PropertyService,
        private _loginService: LoginService) {
        this.UnitId = this._loginService.UserInfo.UnitID;
        this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
        this.updateFormSubscription = this.communicationService.subscribe('updatePropertyForm').subscribe(result => {
            this.zone.run(() => {
                if (this.initialDetails.propertyId != result.data.propertyId) {
                    this.propertyId = result.data.propertyId;
                    this.initialDetails = result.data;
                    sessionStorage.setItem(SessionStorageKeys.LastVisitedStrataProperty, JSON.stringify(this.property))
                    this.getStrataDetails();
                }
            });
        });
    }
    ngOnDestroy() {
        this.updateFormSubscription.unsubscribe();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if(changes.selectedUseTypeID) {
            const isMasterStrataProperty = this.property.CondoTypeID === EnumCondoTypeName.Master;
            this.showAddStrataBtn = this.useTypesToEnableAddStrataBtns.includes(changes.selectedUseTypeID.currentValue) && isMasterStrataProperty;
        }
        if(changes.property) {
            const isMasterStrataProperty = changes.property.currentValue.CondoTypeID === EnumCondoTypeName.Master;
            this.showAddStrataBtn = this.useTypesToEnableAddStrataBtns.includes(this.selectedUseTypeID) && isMasterStrataProperty;
        }
    }
    ngOnInit() {
        const isMasterStrataProperty =this.property.CondoTypeID === EnumCondoTypeName.Master;
        this.showAddStrataBtn = this.useTypesToEnableAddStrataBtns.includes(this.selectedUseTypeID) && isMasterStrataProperty;
        this.editedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.EditedStrataIds)) || [];
        this.metaDataIndexedDBService = new MetaDataIndexedDBService();
        this.isNavigationFromSearch = JSON.parse(sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch));
        if (this.isNavigationFromSearch) {
            this.getVisitedProperties();
        }
        this.getEditedProperties();
        this.propertyId = this.initialDetails.propertyId;
        if (this.UnitId === UnitConversionEnum.Metric) {
            this.BuildingSizeHeader = 'Size (' + this.UnitDisplayTextSize + ')';
            this.BuildingSizeValue = 'MeasuredBuildingSizeSM';
            this.LotSizeValue = 'LotSizeSM';
        } else {
            this.BuildingSizeHeader = 'Size (' + this.UnitDisplayTextSize + ')';
            this.BuildingSizeValue = 'MeasuredBuildingSizeSF';
            this.LotSizeValue = 'LotSizeSF';
        }
        this.getTableHeader();
        if (!this.propertyStrataList || this.propertyStrataList.length <= 0) {
            this.getStrataDetails();
            sessionStorage.setItem(SessionStorageKeys.LastVisitedStrataProperty, JSON.stringify(this.property))
        }
    }

    async getVisitedProperties() {
        try {
            const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.VisitedPropertyIds);
            if (searchData) {
                this.visitedPropertyIds = searchData.value;
            }
        } catch (error) {
            console.error('Error retrieving data:', error);
        }
    }

    async getEditedProperties() {
        try {
            const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.EditedPropertyIds);
            if (searchData) {
                this.editedPropertyIds = searchData.value;
            }
        } catch (error) {
            console.error('Error retrieving data:', error);
        }
    }

    getStrataDetails() {
        const sortedPropertyStrataList = []
        let activeRecord, masterStrataRecord;
        this.propertiesLoading = true;
        const response_strata = this._propertyService.getPropertyStrataDetails(this.initialDetails.propertyId, null, null);
        response_strata.subscribe(result => {
            if (!result.body.error) {
                if (result?.body?.responseData?.length > 0) {
                    this.propertyStrataList = result.body.responseData;
                    const results = this.propertyStrataList || [];
                    activeRecord = results.find(row => row.PropertyID === this.propertyId);
                    masterStrataRecord = results.find(row => row.IsMaster);
                    let strataListWithoutActiveAndMaster = results.filter(row => (!(row.PropertyID === this.propertyId) && !row.IsMaster));
                    strataListWithoutActiveAndMaster = strataListWithoutActiveAndMaster.sort(this.dynamicIntegerSort('StrataUnit', 'Ascending'));
                    sortedPropertyStrataList.push(activeRecord);
                    sortedPropertyStrataList.push(...strataListWithoutActiveAndMaster);
                    if (activeRecord?.PropertyID != masterStrataRecord?.PropertyID) {
                        sortedPropertyStrataList.push(masterStrataRecord);
                    }
                    this.propertyStrataList = sortedPropertyStrataList;
                    this.propertyStrataList = this.propertyStrataList.map(row => ({
                        ...row,
                        sizeValue: getSizeValue(this.propertyStrataList, row, this.UnitId),
                        measurementTypeTitle: getMeasurementTypeTitle(this.propertyStrataList, row),
                        measurementTypeClass: getMeasurementTypeClassname(this.propertyStrataList, row),
                        measurementTypeLabel: getMeasurementTypeLabel(this.propertyStrataList, row)
                    }));
                    const childStratas = this.propertyStrataList.filter(strata => !strata?.IsMaster);
                    if (childStratas.length > 0 && childStratas.every((strata) => strata?.HasNoBuildingFootprints)) {
                        if (this.propertyId === this.propertyStrataList.find(strata => strata?.IsMaster)?.PropertyID) {
                            let commModel = new CommunicationModel();
                            commModel.Key = 'showContributedGBAInTopSection';
                            commModel.data = true;
                            this.communicationService.broadcast(commModel);
                        }
                    }
                    const masterRecord = this.propertyStrataList.find(strata => strata?.IsMaster);
                    if (masterRecord?.IsAverageEstimationEnabled && this.propertyId !== masterRecord?.PropertyID) {
                    let commModel = new CommunicationModel();
                    commModel.Key = 'updateIsAverageEstimationEnabledForStrata';
                    commModel.data = true;
                    this.communicationService.broadcast(commModel);
                    }
                    this.visitedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.VisitedStrataIds)) || [];
                    const strataPropertyIds = sortedPropertyStrataList.map(strata => strata?.PropertyID);
                    const sessionStrataIds = sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds);
                    if (sessionStrataIds) {
                        const sessionStorageStrata = JSON.parse(sessionStrataIds);
                        if (!this.arraysHaveSameElements(sessionStorageStrata, strataPropertyIds)) {
                            this.setSessionStrataIds(strataPropertyIds);
                        }
                    } else {
                        this.setSessionStrataIds(strataPropertyIds);
                    }
                    this.propertiesLoading = false;
                } else {
                    this.propertiesLoading = false;
                }
            }
        }, error => {
            this.propertiesLoading = false;
        });
    }

    setSessionStrataIds(strataIds) {
        sessionStorage.setItem(SessionStorageKeys.StrataPropertyIds, JSON.stringify(strataIds));
        sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    }

    getTableHeader() {
        this.strataTableHeader = [
            { field: 'StrataType', header: 'Type' },
            { field: 'PropertyName', header: 'Property Name' },
            { field: 'Address', header: 'Address' },
            { field: 'StrataUnit', header: 'Strata Unit' },
            { field: this.BuildingSizeValue, header: this.BuildingSizeHeader },
            { field: this.LotSizeValue, header: 'Lot Size' },
            { field: 'ParcelNumbers', header: 'Parcel #' },
            { field: 'PropertyID', header: 'PropertyID' }
        ];
    }

    showPropertySummary(data) {
        this.showPropertyInfo.emit(data.PropertyID);
    }
    sortProperty(sortParam, sortOrder) {
        if (sortParam == 'Address' || sortParam == 'ParcelNumbers') {
            this.propertyStrataList = this.propertyStrataList.sort(this.dynamicSort(sortParam, sortOrder));
        }
        else {
            this.propertyStrataList = this.propertyStrataList.sort(this.dynamicIntegerSort(sortParam, sortOrder));
        }
    }

    dynamicSort(Property: string, sortDirection: string) {
        let sortOrder = 1;
        sortOrder = sortDirection === 'Ascending' ? 1 : -1;
        return function (a, b) {
            const result = (TryParseInt(a[Property], a[Property]) < TryParseInt(b[Property], b[Property]))
                ? -1 : (TryParseInt(a[Property], a[Property]) > TryParseInt(b[Property], b[Property])) ? 1 : 0;
            return result * sortOrder;
        };
    }

    dynamicIntegerSort(Property: string, sortDirection: string) {
        let sortOrder: number;
        sortOrder = sortDirection === 'Ascending' ? 1 : -1;
        return function (a, b) {
            const result = (getInt(a[Property]) < getInt(b[Property])) ? -1 : (getInt(a[Property]) > getInt(b[Property])) ? 1 : 0;
            return result * sortOrder;
        };
    }

    arraysHaveSameElements(arr1, arr2) {
        return this.allElementsInArray1AreInArray2(arr1, arr2) && this.allElementsInArray1AreInArray2(arr2, arr1);
    }

    allElementsInArray1AreInArray2(arr1, arr2) {
        return arr1.every(element => arr2.includes(element));
    }

    isPropertyVisited(property) {

        if (this.isNavigationFromSearch && this.visitedPropertyIds && this.visitedPropertyIds.length > 0) {
            return this.visitedPropertyIds.includes(property);
        } else {
            if (this.visitedStrataIds && this.visitedStrataIds.length > 0) {
                return this.visitedStrataIds.includes(property);
            }
        }

    }

    isPropertyEdited(propertyId) {
        if (this.isNavigationFromSearch && this.editedPropertyIds && this.editedPropertyIds.length > 0) {
            return this.editedPropertyIds.includes(propertyId);
        } else {
            if (this.editedStrataIds && this.editedStrataIds.length > 0) {
                return this.editedStrataIds.includes(propertyId);
            }
        }
    }

    addStrata(isMultiStrata = false) {
        this.property.ContributedGBA_SF = null;
        this.property.MeasuredBuildingSizeSF = null;
        let strataMin = undefined;
        const findLastValue = (arr: any[]): number => {
        if (!arr || arr.length === 0) {
            return 0;
        }
        
        let max = Number.MIN_VALUE;
        for (let i = 0; i < arr.length; i++) {
            const strataUnit = Number(arr[i].StrataUnit);
            if (!isNaN(strataUnit) && strataUnit > max) {
            max = strataUnit;
            }
        }
        return max;
        }
        
        const lastMax = findLastValue(this.propertyStrataList);
        strataMin = `${lastMax+1}`;
        this.addNewStrataUnit.emit({strataMin: strataMin, isMultiStrata, strataList: this.propertyStrataList})
        
    }

    getColumnWidth(header: string): string {
        switch (header) {
        case 'Type': return '120px';
        case 'Property Name': return '160px';
        case 'Address': return '160px';
        case 'Strata Unit': return '95px';
        case this.BuildingSizeHeader: return '90px';
        case 'Lot Size': return '90px';
        case 'Parcel #': return '120px';
        case 'PropertyID': return '100px';
        default: return '120px';
    }
    }
    getScrollHeight() {
        return this.propertyStrataList?.length > 6 ? '50vh' : 'auto';
    }
}

function getInt(value) {
    if (value) {
        const match = value.toString().match(/\d+/);
        if (match && match.length > 0 && match[0]) {
            return TryParseInt(match[0], match[0]);
        }
    }
    if (value === 0) { return 0; } else { return -1; }
}

function TryParseInt(str: any, defaultValue: any): any {
    let retValue = defaultValue;
    if (str !== null) {
        if (str.length > 0) {
            if (!isNaN(str)) {
                retValue = parseInt(str, 10);
            } else {
                retValue = str.toLowerCase();
            }
        }
    } else {
        retValue = '';
    }
    return retValue;
}
