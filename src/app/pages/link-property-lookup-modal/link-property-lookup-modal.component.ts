import { Component, OnInit, Input, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators, NgForm } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { PropertyService } from '../../services/api-property.service';
import { Property } from '../../models/Property';
import { LoginService } from '../../services/login.service';
import { UnitConversionEnum } from '../../enumerations/unitConversion';

@Component({
  selector: 'app-link-property-lookup-modal',
  templateUrl: './link-property-lookup-modal.component.html',
  styleUrls: ['./link-property-lookup-modal.component.scss']
})
export class LinkPropertyLookupModalComponent implements OnInit {
  @ViewChild('#selectedPropertyTable') selectedPropertyTable: TableModule;
  salecompProperties: FormGroup;
  @Input() multiSelect = true;
  @Input() isFreehold = false;
  @Output() onClose = new EventEmitter();
  @Output() onSave: EventEmitter<Array<Property>> = new EventEmitter<Array<Property>>();
  Properties: any = [];
  SearchText: string;
  @Input() isMasterProperty = false;
  @Input() PropId: string = null;
  IsSearchInProgress = false;
  SelectedProperties: Array<Property> = new Array<Property>();
  propertyHeader: any[];
  public totalSelectedPropertyRecords: number;
  public selectPropLoading = false;
  public totalPropertyRecords: number;
  public PropLoading = false;
  UnitDisplayTextSize: any;
  BuildingSizeHeader: string;
  UnitId: number;
  metricUnit: any = 1;
  private _propertyService: PropertyService;
  constructor(private propertyService: PropertyService, private _loginService: LoginService) {
    this._propertyService = propertyService;

    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.UnitId = this._loginService.UserInfo.UnitID;
    this.metricUnit = UnitConversionEnum.Metric;
  }

  ngOnInit() {
    if (this.UnitId === UnitConversionEnum.Metric) {
      this.BuildingSizeHeader = 'Building Size (' + this.UnitDisplayTextSize + ')';

    } else {
      this.BuildingSizeHeader = 'Building Size (' + this.UnitDisplayTextSize + ')';
    }
    this.IsSearchInProgress = false;
    this.salecompProperties = new FormGroup({
      'SearchText': new FormControl('')
    });

    this.getTableHeader();
  }

  getTableHeader() {
    this.propertyHeader = [
      { field: 'PropertyID', header: 'Property ID' },
      { field: 'PropertyName', header: 'Property Name' },
      { field: 'Address', header: 'Address' },
      { field: 'CityName', header: 'City' },
      { field: 'PropertyUse', header: 'Property Use' },
      { field: 'MeasuredBuildingSizeSF', header: this.BuildingSizeHeader },
      { field: 'MeasuredBuildingSizeSM', header: this.BuildingSizeHeader }
    ];
  }

  getProperties() {
    this.IsSearchInProgress = true;
    if (this.SearchText.length >= 3) {
      let response_roles;
      if (this.isFreehold) {
        response_roles = this._propertyService.getFreeHoldPropertyDetails(this.SearchText, this.isMasterProperty);
      } else {
        response_roles = this._propertyService.GetPropertydetails(this.SearchText, this.isMasterProperty);
      }
      response_roles.subscribe(result => {
        this.Properties = result.body.responseData || [];
        this.PropId = JSON.parse(this.PropId);
        if (!!this.PropId && this.isMasterProperty) {
          this.Properties = this.Properties.filter(x => x.PropertyID !== this.PropId);
        }
        this.IsSearchInProgress = false;
      });
    } else {
      this.IsSearchInProgress = false;
    }
  }

  addProperty(value, isChecked: boolean, indexVal: number) {
    if (this.multiSelect === true) {
      if (isChecked) {
        this.SelectedProperties.push(value);
      } else {
        const intialObj = this.SelectedProperties.find(x => x.PropertyID === value.PropertyID);
        const index: number = this.SelectedProperties.indexOf(intialObj);
        this.SelectedProperties.splice(index, 1);
      }
    } else {
      if (isChecked) {
        this.changeChecked(indexVal);
        this.SelectedProperties = new Array<Property>();
        this.SelectedProperties.push(value);
      } else {
        this.SelectedProperties.pop();
      }
    }
  }

  changeChecked(indexVal: number) {
    for (let i = 0; i < this.Properties.length; i++) {
      if (i !== indexVal) {
        this.Properties[i].IsMultiSelected = false;
      }
    }
  }

  SaveCompProperties() {
    this.onSave.emit(this.SelectedProperties);
    this.onClose.emit();
  }

  closeSaleCompProperties() {
    this.onClose.emit();
  }

  getColumnWidth(header: string): string {
    const BuildingSizeHeader = this.BuildingSizeHeader;
    switch (header) {
      case 'Property ID': return '200px';
      case 'Property Name': return '150px';
      case 'Address': return '100px';
      case 'City': return '150px';
      case 'Property Use': return '150px';
      case BuildingSizeHeader: return '150px';
      default: return '120px';
    }
  }

  getScrollHeight() {
    return this.Properties?.length > 10 ? '40vh' : 'auto';
  }
}
