<div>
    <label>
      There are Sales Transactions associated with PID
      {{ displayPropertyId }} that have Sold sqm size conflicts with the property
      record [Contributed GBA or Aerial Measurement]. Please select the Sales
      Transactions to be updated with the revised GBA Building Size.
    </label>
  
    <p-table
      class="mt-3"
      [value]="sales"
      selectionMode="multiple"
      [(selection)]="selectedSales"
      [loading]="saleLoading"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 3rem">
            <p-tableHeaderCheckbox> </p-tableHeaderCheckbox>
          </th>
          <th style="width: 8rem">Sale ID</th>
          <th style="width: 12rem">Legal Description</th>
          <th style="width: 8rem">Date</th>
          <th style="width: 6rem">Unit</th>
          <th style="width: 8rem">Type</th>
          <th style="width: 10rem">Sold Price</th>
          <th style="width: 10rem">Sold Price/sqm</th>
          <th style="width: 8rem">Sold SQM</th>
          <th style="width: 15rem">Building Size</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-sale>
        <tr>
          <td>
            <p-tableCheckbox [value]="sale"></p-tableCheckbox>
          </td>
          <td>{{ sale.SaleID }}</td>
          <td>{{ sale.LegalDescription }}</td>
          <td>{{ sale.DeedOrSaleDate | date : "dd/MM/yyyy" }}</td>
          <td>{{ sale.CondoUnit }}</td>
          <td>{{ sale.SaleTypeName }}</td>
          <td>{{ sale.SoldPrice | currency }}</td>
          <td>{{ sale.SalePricePerSM | currency }}</td>
          <td>{{ sale.SoldSM }}</td>
          <td>
            <ng-container
              *ngIf="property.HasNoBuildingFootprints > 0; else buildingSm"
            >
              {{ property.ContributedGBA_SM }}
  
            </ng-container>
            <ng-container #buildingSm>
              {{ property.BuildingSizeSM }}
  
            </ng-container>
          </td>
        </tr>
      </ng-template>
    </p-table>
  
    <div class="d-flex justify-content-end action-buttons">
      <button
        type="button"
        class="btn btn-primary me-2"
        (click)="cancelConfirmationPopup()"
      >
        Cancel
      </button>
      <button
        type="button"
        class="btn btn-primary"
        (click)="onUpdateEnrichment()"
        [disabled]="!selectedSales.length"
      >
        Update
      </button>
    </div>
  </div>