import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { Property } from '../../../models/Property';
import { LoginService } from '../../../services/login.service';
import { Yes<PERSON>rNoList, IndustrialYesNoFields } from '../../../common/constants';
import { buildChangeLog, clearHeightMaxValidator, clearHeightMinValidator, getPreviousData, validateIntegerInput, validatePasteInput } from '../../../utils';
import { EnumCondoTypeName } from '../../../enumerations/condoType';
import { YesOrNoService} from '../../../services/yes-or-no.service';
import { RetailControls } from '../../../enumerations/RetailControlKeys';
import { distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-height-loading-details',
  templateUrl: './height-loading-details.component.html',
  styleUrls: ['./height-loading-details.component.css']
})
export class HeightLoadingDetailsComponent implements OnInit {

  @Input() retailForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;
  clearHeightMinError = false;
  EnumCondoTypeNames = EnumCondoTypeName;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  showRetailFrontage = false;
  retailFrontagePolyline: { distance: number, polyline: any };

  constructor(private _loginService: LoginService, public yesOrNoService: YesOrNoService) { }

  ngOnInit(): void {
    this.retailForm?.get(RetailControls?.ClearHeightMax)?.setValidators([
      Validators.min(3),
      Validators.max(999),
      clearHeightMaxValidator
    ]);
    this.retailForm?.get(RetailControls?.ClearHeightMax)?.updateValueAndValidity();
    this.retailForm?.get(RetailControls?.ClearHeightMin)?.setValidators([
      Validators.min(3),
      Validators.max(999),
      clearHeightMinValidator
    ]);
    this.retailForm?.get(RetailControls?.ClearHeightMin)?.updateValueAndValidity();
    this.retailForm.get(RetailControls?.ClearHeightMax)?.valueChanges?.pipe(distinctUntilChanged()).subscribe(() => {  // Only trigger if the value has actually changed
      this.retailForm.get(RetailControls?.ClearHeightMin)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
    });
    this.retailForm.get(RetailControls?.ClearHeightMin)?.valueChanges?.pipe(distinctUntilChanged()) // Only trigger if the value has actually changed
      .subscribe(() => {
        this.retailForm.get(RetailControls?.ClearHeightMax)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
      });
    this.retailForm.get(RetailControls?.MeasuredBuildingSizeSF)?.valueChanges?.pipe(distinctUntilChanged())
      .subscribe(() => {
        this.onParkingChange();
      });
  }


  getDropdownFromLookup(field: string) {
    if (IndustrialYesNoFields.includes(field)) {
      return YesOrNoList;
    }
  }

  onParkingChange() {
    if (this.property.ParkingSpaces && this.property.MeasuredBuildingSizeSF) {
      this.property.ParkingRatio = (this.property.ParkingSpaces * 100) / this.property.MeasuredBuildingSizeSF;
      this.property.ParkingRatio = parseFloat(Number(this.property.ParkingRatio).toFixed(2));
    }
  }

  selectedValue(Type, event, ValueName) {
    let value = null;
    if (!!event) {
      value = event[ValueName];
    } else {
      value = null;
    }
    const id = this.propertyCopy[Type];
    const dropdownData = this.getDropdownFromLookup(Type);

    const previousData = getPreviousData(Type, id, dropdownData)
    buildChangeLog(Type, this.dataArray, value, previousData, this._loginService.UserInfo.EntityID);
  }
  onRetailFrontageClicked() {
    this.showRetailFrontage = true;
  }

  onReatilFrontageSave({ distance, polyline }) {
    if (distance) {
      this.property.RetailFrontage = distance;
      this.retailForm.get(RetailControls?.RetailFrontage)?.markAsDirty();
      this.retailFrontagePolyline = { distance, polyline }
    }
    this.showRetailFrontage = false;
  }
}


