import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { LoginService } from '../../../services/login.service';
import { Property } from '../../../models/Property';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { ResearchType } from '../../../enumerations/researchType';
import { stringToArray, buildChangeLog, getPreviousData, isValidWebsite, updateValidation, validatePasteInput, validateIntegerInput } from '../../../utils';
import { YesNoFields, OfficeChangeLogFields, YesOrNoList, BindNamesWithLookupName } from '../../../common/constants';
import { EnumCondoTypeName } from '../../../enumerations/condoType';
import { YesOrNoService} from '../../../services/yes-or-no.service';
import { debounceTime } from 'rxjs/operators';
import { DebounceTimeConfig } from '../../../enumerations/debounce-time';
import { UseTypes } from '../../../../app/enumerations/useTypes';

@Component({
  selector: 'app-parking-lifts-details',
  templateUrl: './parking-lifts-details.component.html',
  styleUrls: ['./parking-lifts-details.component.css']
})
export class ParkingLiftsDetailsComponent implements OnInit {
  @Input() officeForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;
  EnumCondoTypeNames = EnumCondoTypeName;
  amenitiesType;
  amenitiesTypeCopy;
  isBuildingWebsiteValueValid = true;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
   propertyUseTypes = UseTypes;

  constructor(private _loginService:LoginService, public yesOrNoService: YesOrNoService) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.property || changes.condo) {
      this.toggleFeatureBasedOnCondo();
    }
    if (changes.property) {
      this.updateAmenitiesType(changes.property?.currentValue);
    }
  }

  ngOnInit(): void {
    this.updateAmenitiesType(this.property);
    this.officeForm.get(OfficeControls.HasReservedParkingSpaces).valueChanges.subscribe(
      (HasReservedCoveredParking) => {
        this.updateHasReservedCoveredParkingValidations(HasReservedCoveredParking);
      }
    );
    this.officeForm.get(OfficeControls.HasSprinkler).valueChanges.subscribe(
      (HasSprinkler) => {
        this.updateHasSprinklerValidations(HasSprinkler);
      }
    );
    this.officeForm.get(OfficeControls.HasUnreservedParkingSpaces).valueChanges.subscribe(
      (Hasunreservedcoveredparking) => {
        this.updateHasUnReservedCoveredParkingValidations(Hasunreservedcoveredparking)
      }
    );

     this.officeForm.get(OfficeControls.BuildingWebsite)?.valueChanges?.subscribe(
          (value) => {
            this.isBuildingWebsiteValueValid = isValidWebsite(value);
          }
        );

    this.officeForm?.get(OfficeControls?.TypicalFloorSizeSM)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.TypicalFloorSizeSourceID, 'TypicalFloorSizeSourceID', OfficeControls, this.property, this.officeForm);
      }
    );
    this.officeForm?.get(OfficeControls?.TypicalFloorSizeSM)?.updateValueAndValidity();
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field];
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  updateHasReservedCoveredParkingValidations(HasReservedCoveredParking) {
    if (HasReservedCoveredParking !== 1) {
      this.officeForm.controls[OfficeControls.ReservedParkingSpaces].setValue(null);
      this.officeForm.controls[OfficeControls.ReservedParkingSpacesRatePerMonth].setValue(null);
      this.officeForm.controls[OfficeControls.ReservedParkingSpaces].disable();
      this.officeForm.controls[OfficeControls.ReservedParkingSpacesRatePerMonth].disable();
    } else {
      this.officeForm.controls[OfficeControls.ReservedParkingSpaces].enable();
      this.officeForm.controls[OfficeControls.ReservedParkingSpacesRatePerMonth].enable();
    }
    this.officeForm.get(OfficeControls.ReservedParkingSpaces).updateValueAndValidity();
    this.officeForm.get(OfficeControls.ReservedParkingSpacesRatePerMonth).updateValueAndValidity();
  }

  updateHasSprinklerValidations(HasSprinkler) {
    if (HasSprinkler !== 1) {
      this.officeForm.controls[OfficeControls.SprinklerTypeID].disable();
      this.officeForm.get(OfficeControls.SprinklerTypeID).clearValidators();
      this.property.SprinklerTypeID = null;
    } else {
      this.officeForm.controls[OfficeControls.SprinklerTypeID].enable();
      if (this.propertyStatus && this.propertyStatus !== ResearchType.Hidden) {
        this.officeForm.get(OfficeControls.SprinklerTypeID).setValidators([Validators.required]);
      }
    }
    this.officeForm.get(OfficeControls.SprinklerTypeID).updateValueAndValidity();
  }

  updateHasUnReservedCoveredParkingValidations(Hasunreservedcoveredparking) {
    if (Hasunreservedcoveredparking !== 1) {
      this.officeForm.controls[OfficeControls.UnreservedParkingSpaces].setValue(null);
      this.officeForm.controls[OfficeControls.UnreservedParkingSpacesRatePerMonth].setValue(null);
      this.officeForm.controls[OfficeControls.UnreservedParkingSpaces].disable();
      this.officeForm.controls[OfficeControls.UnreservedParkingSpacesRatePerMonth].disable();
    } else {
      this.officeForm.controls[OfficeControls.UnreservedParkingSpaces].enable();
      this.officeForm.controls[OfficeControls.UnreservedParkingSpacesRatePerMonth].enable();
    }
    this.officeForm.get(OfficeControls.UnreservedParkingSpaces).updateValueAndValidity();
    this.officeForm.get(OfficeControls.UnreservedParkingSpacesRatePerMonth).updateValueAndValidity();
  }

  updateAmenitiesType(property) {
    this.amenitiesType = stringToArray(property?.AmenitiesTypeIDs)
    this.amenitiesTypeCopy = JSON.parse(JSON.stringify(this.amenitiesType));
    this.property.AmenitiesType = this.amenitiesType && this.amenitiesType.join(',')  
  }

  toggleFeatureBasedOnCondo(){
    if (this.condo === this.EnumCondoTypeNames.Master_Freehold) {
      this.officeForm.controls[OfficeControls.AmenitiesTypeID]?.disable();
    } else {
      this.officeForm.controls[OfficeControls.AmenitiesTypeID]?.enable();
    }
  }

  onValueChange(type, event, valueName) {
    let value = null;
    if (!!event) {
      value = event[valueName];
    } else {
      value = null;
    }
    const id = this.propertyCopy[type];
    let previousData;
    const dropdownData = this.getDropdownFromLookup(type);
    if (dropdownData) {
      if (type === OfficeChangeLogFields?.AmenitiesTypeID) {
        type = 'AmenitiesType'
        this.property.AmenitiesType = this.amenitiesType.join(',');
        let currValue = '', prevValue = '';
        if (!!this.amenitiesType && this.amenitiesType.length > 0) {
          this.amenitiesType.forEach(element => {
            currValue = currValue + dropdownData.filter(x => x.AmenitiesTypeID === element)[0].AmenitiesTypeName + ',';
          });
        }
        if (!!this.amenitiesTypeCopy && this.amenitiesTypeCopy.length > 0) {
          this.amenitiesTypeCopy.forEach(element => {
            prevValue = prevValue + dropdownData.filter(x => x.AmenitiesTypeID === element)[0].AmenitiesTypeName + ',';
          });
        }        
        if (currValue.length !== 0) {
          currValue = currValue.substring(0, currValue.length - 1);
        }
        if (prevValue.length !== 0) {
          prevValue = prevValue.substring(0, prevValue.length - 1);
        }
        value = currValue;
        previousData = prevValue;
      } else {
        previousData = getPreviousData(type, id, dropdownData)
      }
    }
    buildChangeLog(type, this.dataArray, value, previousData, this._loginService.UserInfo.EntityID);
  }
}
