<div [formGroup]="useTypeForm" class="occupancy-wrapper">
  <div>
    <div class="title">Occupancy</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Tenancy</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="TenancyTypeID" [items]="getDropdownFromLookup('TenancyTypeID')"
              [virtualScroll]="true" bindLabel="TenancyName" bindValue="TenancyID"
              placeholder="--Select--" [(ngModel)]="property.TenancyTypeID"
              labelForId="TenancyTypeID" (change)="onValueChange('TenancyTypeID', $event, 'TenancyName')">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" title="{{rollupMasterFreeholdFieldsObject?.TenancyName}}" [value]="rollupMasterFreeholdFieldsObject?.TenancyName" type="text" readonly class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Owner Occupied</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputIsOwnerOccupied">
              <input type="checkbox" formControlName="IsOwnerOccupied" [(ngModel)]="property.IsOwnerOccupied" (change)="checkboxChange($event, 'IsOwnerOccupied')">
            </ng-container>
            <ng-template #masterRollupInputIsOwnerOccupied>
              <input type="checkbox" title="{{rollupMasterFreeholdFieldsObject?.IsOwnerOccupied}}"
                [checked]="rollupMasterFreeholdFieldsObject?.IsOwnerOccupied" [disabled]="true">
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Occupancy %</div>
          <div class="col-md-7">
            <input type="number" readonly formControlName="OccupancyPercent" [(ngModel)]="property.OccupiedPercentage" class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Size</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputBuildingSF">
              <input type="number" readonly formControlName="MeasuredBuildingSizeSF" [(ngModel)]="property.MeasuredBuildingSizeSF" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputBuildingSF>
              <input type="number" readonly [value]="rollupMasterFreeholdFieldsObject?.MeasuredBuildingSizeSF" title="{{rollupMasterFreeholdFieldsObject?.MeasuredBuildingSizeSF}}"
                class="form-control">
            </ng-template>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Government Interest</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="GovernmentInterestID" [items]="getDropdownFromLookup('GovernmentInterestID')"
              [virtualScroll]="true" bindLabel="GovernmentInterestName" bindValue="GovernmentInterestID"
              placeholder="--Select--" [(ngModel)]="property.GovernmentInterestID"
              labelForId="GovernmentInterestID" (change)="onValueChange('GovernmentInterestID', $event, 'GovernmentInterestName')">
            </ng-select>
            <input type="text" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" title="{{rollupMasterFreeholdFieldsObject?.GovernmentInterestName}}" [value]="rollupMasterFreeholdFieldsObject?.GovernmentInterestName" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Build to Suit/Spec</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="BuildSpecStatusID" [items]="getDropdownFromLookup('BuildSpecStatusID')"
              [virtualScroll]="true" bindLabel="BuildSpecStatusName" bindValue="BuildSpecStatusID"
              placeholder="--Select--" [(ngModel)]="property.BuildSpecStatusID"
              labelForId="BuildSpecStatusID" (change)="onValueChange('BuildSpecStatusID', $event, 'BuildSpecStatusName')">
            </ng-select>
            <input type="text" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" title="{{rollupMasterFreeholdFieldsObject?.BuildSpecStatusName}}" [value]="rollupMasterFreeholdFieldsObject?.BuildSpecStatusName" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Vacancy</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputVacancy">
              <input type="text" formControlName="Vacancy" [(ngModel)]="property.Vacancy" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputVacancy>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.Vacancy" title="{{rollupMasterFreeholdFieldsObject?.Vacancy}}" class="form-control"
                readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Outgoings/SQM</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupOutgoings">
              <input type="text" formControlName="TIAllowance" [(ngModel)]="property.TIAllowance" class="form-control" numericOnly allowDecimal="true" allowNegative="false"
              (paste)="validatePasteInput($event, true)" (keypress)="validateIntegerInput($event, true)">
            </ng-container>
            <ng-template #masterRollupOutgoings>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.TIAllowance" title="{{rollupMasterFreeholdFieldsObject?.TIAllowance}}" class="form-control"
                readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="title">Other Details</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">HVAC</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="HVAC" [items]="getDropdownFromLookup('HVAC')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HVAC"
              labelForId="HVAC" (change)="onValueChange('HVAC',$event,'label')">
            </ng-select>
            <input type="text" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HVAC)" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">HVAC Type</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="HVACTypeID" [items]="getDropdownFromLookup('HVACTypeID')"
              [virtualScroll]="true" bindLabel="HVACTypeName" bindValue="HVACTypeID"
              placeholder="--Select--" [(ngModel)]="property.HVACTypeID"
              labelForId="HVACTypeID" (change)="onValueChange('HVACTypeID', $event, 'HVACTypeName')"
              [ngClass]="{'error-field':(useTypeForm?.controls['HVACTypeID']?.enabled && !useTypeForm?.controls['HVACTypeID']?.valid)}">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" readonly title="{{rollupMasterFreeholdFieldsObject?.HVACTypeName}}" [value]="rollupMasterFreeholdFieldsObject?.HVACTypeName" type="text" class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Roof Type</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="RoofTypeID" [items]="getDropdownFromLookup('RoofTypeID')"
              [virtualScroll]="true" bindLabel="RoofTypeName" bindValue="RoofTypeID"
              placeholder="--Select--" [(ngModel)]="property.RoofTypeID"
              labelForId="RoofTypeID" (change)="onValueChange('RoofTypeID', $event, 'RoofTypeName')">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" readonly title="{{rollupMasterFreeholdFieldsObject?.RoofTypeName}}" [value]="rollupMasterFreeholdFieldsObject?.RoofTypeName" type="text" class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Has Solar</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputHasSolar">
              <input type="checkbox" formControlName="HasSolar" [(ngModel)]="property.HasSolar"
                (change)="checkboxChange($event, 'HasSolar')">
            </ng-container>
            <ng-template #masterRollupInputHasSolar>
              <input type="checkbox" [checked]="rollupMasterFreeholdFieldsObject?.HasSolar" [disabled]="true">
            </ng-template>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Book Value</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputBookValue">
              <input formControlName="BookValue" [(ngModel)]="property.BookValue" class="form-control" type="text" numericOnly
                allowDecimal="false" allowNegative="false">
            </ng-container>
            <ng-template #masterRollupInputBookValue>
              <input [value]="rollupMasterFreeholdFieldsObject?.BookValue" title="{{rollupMasterFreeholdFieldsObject?.BookValue}}" class="form-control" type="text" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Book Value Date</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputBookValueDate">
              <input class="form-control" placeholder="Click to select a date" formControlName="BookValueDate" angular-mydatepicker
                name="BookValueDate" (click)="bookValueDate.toggleCalendar()" [(ngModel)]="property.BookValueDate"
                (dateChanged)="onDateChange('BookValueDate', $event)" [options]="myDpOptions"
                #bookValueDate="angular-mydatepicker" />
              <i class="far fa-calendar-alt" aria-hidden="true" (click)="bookValueDate.toggleCalendar()"></i>
            </ng-container>
            <ng-template #masterRollupInputBookValueDate>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.BookValueDate" title="{{rollupMasterFreeholdFieldsObject?.BookValueDate}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"> Australia DDA</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputIsADAAccessible">
              <input type="checkbox" formControlName="IsADAAccessible" [(ngModel)]="property.IsADAAccessible" (change)="checkboxChange($event, 'IsADAAccessible')">
            </ng-container>
            <ng-template #masterRollupInputIsADAAccessible>
              <input type="checkbox" [checked]="rollupMasterFreeholdFieldsObject?.IsADAAccessible" [disabled]="true">
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="title">INTERNAL ONLY</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-2 label"></div>
          <div class="col-md-10">
            <div class="col-md-12 error" *ngIf="hasConstructionDateError">
              {{ constructionDateErrorMsg }}
            </div>
          </div>
        </div>
      </div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Construction Start</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputConstructionStartDate">
              <input class="form-control" placeholder="Click to select a date"
              formControlName="ConstructionStartDate" angular-mydatepicker name="ConstructionStartDate"
              (click)="constructionStartDate.toggleCalendar()" [(ngModel)]="property.ConstructionStartDate"
              (dateChanged)="onDateChange('ConstructionStartDate', $event)" [options]="myDpOptions"
              #constructionStartDate="angular-mydatepicker"
              [ngClass]="{'error-field':hasConstructionDateError}"/>
              <i class="far fa-calendar-alt" aria-hidden="true" (click)="constructionStartDate.toggleCalendar()"></i>
            </ng-container>
            <ng-template #masterRollupInputConstructionStartDate>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ConstructionStartDate" title="{{rollupMasterFreeholdFieldsObject?.ConstructionStartDate}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Est. Completion</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputEstCompletionDate">
              <input class="form-control" placeholder="Click to select a date" formControlName="EstCompletionDate"
                angular-mydatepicker name="EstCompletionDate" (click)="estCompletionDate.toggleCalendar()"
                [(ngModel)]="property.EstCompletionDate" (dateChanged)="onDateChange('EstCompletionDate', $event)"
                [options]="myDpOptions" #estCompletionDate="angular-mydatepicker" />
              <i class="far fa-calendar-alt" aria-hidden="true" (click)="estCompletionDate.toggleCalendar()"></i>
            </ng-container>
            <ng-template #masterRollupInputEstCompletionDate>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.EstCompletionDate" title="{{rollupMasterFreeholdFieldsObject?.EstCompletionDate}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Actual Completion</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputActualCompletion">
              <input class="form-control" placeholder="Click to select a date" formControlName="ActualCompletion"
                angular-mydatepicker name="ActualCompletion" (click)="actualCompletionDate.toggleCalendar()"
                [(ngModel)]="property.ActualCompletion" (dateChanged)="onDateChange('ActualCompletion', $event)"
                [options]="myDpOptions" #actualCompletionDate="angular-mydatepicker" />
              <i class="far fa-calendar-alt" aria-hidden="true" (click)="actualCompletionDate.toggleCalendar()"></i>
            </ng-container>
            <ng-template #masterRollupInputActualCompletion>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ActualCompletion" title="{{rollupMasterFreeholdFieldsObject?.ActualCompletion}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Include in Tracked Set</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputIncludeinAnalytics">
              <input type="checkbox" formControlName="IncludeinAnalytics" [(ngModel)]="property.IncludeinAnalytics"
                (change)="checkboxChange($event, 'IncludeinAnalytics')">
            </ng-container>
            <ng-template #masterRollupInputIncludeinAnalytics>
              <input type="checkbox" [checked]="rollupMasterFreeholdFieldsObject?.IncludeinAnalytics" [disabled]="true">
            </ng-template>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Internal Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="InternalComments" [(ngModel)]="property.InternalComments" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Current Title Reference #</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputCurrentTitle">
              <input formControlName="CurrentTitle" [(ngModel)]="property.CurrentTitle" class="form-control" type="text" numericOnly
                allowDecimal="false" allowNegative="false">
            </ng-container>
            <ng-template #masterRollupInputCurrentTitle>
              <input [value]="rollupMasterFreeholdFieldsObject?.CurrentTitle" title="{{rollupMasterFreeholdFieldsObject?.CurrentTitle}}" class="form-control" type="text" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row ">
          <div class="col-md-5 label">Title Reference Date</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputTitleReferenceDate">
              <input class="form-control" placeholder="Click to select a date" formControlName="TitleReferenceDate"
                angular-mydatepicker name="TitleReferanceDate" (click)="titleReferance.toggleCalendar()"
                [(ngModel)]="property.TitleReferenceDate" (dateChanged)="onDateChange('TitleReferenceDate', $event)"
                [options]="myDpOptions" #titleReferance="angular-mydatepicker" />
              <i class="far fa-calendar-alt" aria-hidden="true" (click)="titleReferance.toggleCalendar()"></i>
            </ng-container>
            <ng-template #masterRollupInputTitleReferenceDate>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.TitleReferenceDate" title="{{rollupMasterFreeholdFieldsObject?.TitleReferenceDate}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
