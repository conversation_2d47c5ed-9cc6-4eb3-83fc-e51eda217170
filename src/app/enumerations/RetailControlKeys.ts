export enum RetailControls{
    SpecificUse = 'SpecificUse',
    ContributedGBA_SF = 'ContributedGBA_SF',
    OfficeSF = 'OfficeSF',
    OfficeNLASource = 'OfficeNLASource',
    MeasuredBuildingSizeSF = 'MeasuredBuildingSizeSF',
    GLAR_SF = 'GLAR_SF',
    RetailGLARSource = 'RetailGLARSource',
    ContributedGBASource = 'ContributedGBASource',
    SmallestFloor = 'SmallestFloor',
    LargestFloor = 'LargestFloor',
    YearBuilt = 'YearBuilt',
    YearRenovated = 'YearRenovated',
    EnergyStarRatingID = 'EnergyStarRatingID',
    WaterStarRatingID = 'WaterStarRatingID',
    GreenStarRatingID = 'GreenStarRatingID',
    GRESBScoreMin = 'GRESBScoreMin',
    GRESBScoreMax = 'GRESBScoreMax',
    HasSprinkler = 'HasSprinkler',
    SprinklerTypeID = 'SprinklerTypeID',
    Lifts = 'Lifts',
    LiftsCount = 'LiftsCount',
    PowerType = 'PowerType',
    PowerComments = 'PowerComments',
    BuildingComments = 'BuildingComments',
    BuildingWebsite = 'BuildingWebsite',
    RetailFrontage = 'RetailFrontage',
    TrafficCount = 'TrafficCount',
    Anchors = 'Anchors',
    TotalAnchorSF = 'TotalAnchorSF',
    CoveredParking = 'CoveredParking',
    UncoveredParking = 'UncoveredParking',
    ParkingSpaces = 'ParkingSpaces',
    ParkingRatio = 'ParkingRatio',
    GradeLevelDriveIn = 'GradeLevelDriveIn',
    DockHigh = 'DockHigh',
    Truckwell = 'Truckwell',
    ClearHeightMin = 'ClearHeightMin',
    ClearHeightMax = 'ClearHeightMax',
    HasYard = 'HasYard',
    HasYardFenced = 'HasYardFenced',
    IsVented = 'IsVented',
    UnreservedParkingSpaces = 'UnreservedParkingSpaces',
    ReservedParkingSpaces = 'ReservedParkingSpaces',
    TenancyTypeID = 'TenancyTypeID',
    IsOwnerOccupied = 'IsOwnerOccupied',
    OccupancyPercent = 'OccupancyPercent',
    GovernmentInterestID = 'GovernmentInterestID',
    BuildSpecStatusID = 'BuildSpecStatusID',
    Vacancy = 'Vacancy',
    HVAC = 'HVAC',
    HVACTypeID = 'HVACTypeID',
    RoofTypeID = 'RoofTypeID',
    HasSolar = 'HasSolar',
    BookValue = 'BookValue',
    BookValueDate = 'BookValueDate',
    IsADAAccessible = 'IsADAAccessible',
    ConstructionStartDate = 'ConstructionStartDate',
    EstCompletionDate = 'EstCompletionDate',
    ActualCompletion = 'ActualCompletion',
    IncludeinAnalytics = 'IncludeinAnalytics',
    InternalComments = 'InternalComments',
    CurrentTitle = 'CurrentTitle',
    TitleReferenceDate = 'TitleReferenceDate',
    ContributedSourceComments = 'ContributedSourceComments',
    TIAllowance = 'TIAllowance',
    IsLettableOverrideEnabled = 'IsLettableOverrideEnabled',
    TotalLettableSourceID = 'TotalLettableSourceID',
    TotalLettableSize_SF = 'TotalLettableSize_SF'
}
